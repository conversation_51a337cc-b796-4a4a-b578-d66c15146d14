#!/usr/bin/env python3
"""
AutoGPT Trader - Direct Trading System Launch
Bypasses PostgreSQL server setup and launches trading directly
"""

import os
import sys
import asyncio
import logging
import time
from pathlib import Path
from datetime import datetime, timezone

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("autogpt_trader")

# Setup X drive environment
def setup_x_drive_environment():
    """Setup X drive environment and paths"""
    X_DRIVE_PROJECT = Path("X:/autogpt_trade_project/The_real_deal/autogpt-trader")
    X_DRIVE_SRC = X_DRIVE_PROJECT / "src"
    
    # Add to Python path
    x_drive_paths = [str(X_DRIVE_PROJECT), str(X_DRIVE_SRC)]
    for path in x_drive_paths:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    # Set environment variables
    os.environ["PROJECT_ROOT"] = str(X_DRIVE_PROJECT)
    os.environ["SRC_DIR"] = str(X_DRIVE_SRC)
    os.environ["PYTHONPATH"] = f"{X_DRIVE_SRC};{X_DRIVE_PROJECT}"
    
    print(f"✅ [PATH] Project root: {X_DRIVE_PROJECT}")
    print(f"✅ [PATH] Source dir: {X_DRIVE_SRC}")
    
    return X_DRIVE_PROJECT, X_DRIVE_SRC

# Setup environment
PROJECT_ROOT, SRC_DIR = setup_x_drive_environment()

# Force live trading mode
def enforce_live_trading_mode():
    """Enforce live trading mode - NO SIMULATION/MOCK/TEST allowed"""
    os.environ["LIVE_TRADING"] = "true"
    os.environ["REAL_MONEY_TRADING"] = "true" 
    os.environ["NO_SIMULATION"] = "true"
    os.environ["AGGRESSIVE_TRADING"] = "true"
    print("💰 [LIVE-TRADING] Real money trading mode enforced - NO SIMULATION ALLOWED")

enforce_live_trading_mode()

class AutoGPTTrader:
    """Main AutoGPT Trading System"""
    
    def __init__(self):
        self.running = False
        self.exchange_clients = {}
        self.trading_engines = {}
        self.neural_components = {}
        self.data_feeds = {}
        
    async def initialize_system(self):
        """Initialize the complete trading system"""
        try:
            logger.info("🚀 [STARTUP] AutoGPT Trader - REAL MONEY TRADING SYSTEM")
            logger.info("💰 [WARNING] REAL MONEY TRADING ACTIVE")
            
            # Test PostgreSQL connection (Docker)
            await self._test_postgresql_connection()
            
            # Initialize exchange clients
            await self._initialize_exchange_clients()
            
            # Initialize trading engines
            await self._initialize_trading_engines()
            
            # Initialize neural components
            await self._initialize_neural_components()
            
            # Initialize data feeds
            await self._initialize_data_feeds()
            
            logger.info("✅ [STARTUP] System initialization completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ [STARTUP] System initialization failed: {e}")
            return False
    
    async def _test_postgresql_connection(self):
        """Test PostgreSQL connection to Docker container"""
        try:
            from src.monitoring.models import engine
            with engine.connect() as conn:
                result = conn.execute("SELECT 1")
                logger.info("✅ [DATABASE] PostgreSQL connected successfully")
        except Exception as e:
            logger.warning(f"⚠️ [DATABASE] PostgreSQL connection issue: {e}")
            logger.info("🔄 [DATABASE] Continuing with limited database functionality")
    
    async def _initialize_exchange_clients(self):
        """Initialize exchange clients"""
        try:
            # Initialize Bybit
            try:
                from src.exchanges.bybit import BybitTrader
                self.exchange_clients['bybit'] = BybitTrader()
                logger.info("✅ [EXCHANGE] Bybit client initialized")
            except Exception as e:
                logger.warning(f"⚠️ [EXCHANGE] Bybit initialization failed: {e}")
            
            # Initialize Coinbase
            try:
                from src.exchanges.coinbase import CoinbaseClient
                self.exchange_clients['coinbase'] = CoinbaseClient({})
                logger.info("✅ [EXCHANGE] Coinbase client initialized")
            except Exception as e:
                logger.warning(f"⚠️ [EXCHANGE] Coinbase initialization failed: {e}")
                
        except Exception as e:
            logger.error(f"❌ [EXCHANGE] Exchange initialization failed: {e}")
    
    async def _initialize_trading_engines(self):
        """Initialize trading engines"""
        try:
            # Initialize continuous trading loop
            from src.trading.continuous_trading import ContinuousTradingLoop
            
            config = {
                'loop_interval': 3,
                'profit_target_per_hour': 50.0,
                'max_balance_usage': 0.8,
                'hot_reload_enabled': True
            }
            
            self.trading_engines['continuous'] = ContinuousTradingLoop(
                exchange_clients=self.exchange_clients,
                config=config
            )
            
            logger.info("✅ [TRADING] Continuous trading engine initialized")
            
        except Exception as e:
            logger.error(f"❌ [TRADING] Trading engine initialization failed: {e}")
    
    async def _initialize_neural_components(self):
        """Initialize neural network components"""
        try:
            # Initialize hybrid trading agent
            try:
                from src.neural.hybrid_agent import HybridTradingAgent
                neural_config = {
                    'learning_rate': 0.001,
                    'batch_size': 32,
                    'memory_size': 10000
                }
                self.neural_components['hybrid_agent'] = HybridTradingAgent(neural_config)
                logger.info("✅ [NEURAL] Hybrid trading agent initialized")
            except Exception as e:
                logger.warning(f"⚠️ [NEURAL] Hybrid agent initialization failed: {e}")
                
        except Exception as e:
            logger.error(f"❌ [NEURAL] Neural component initialization failed: {e}")
    
    async def _initialize_data_feeds(self):
        """Initialize data feeds"""
        try:
            # Initialize live data fetcher
            try:
                from src.data_feeds.live_data_fetcher import LiveDataFetcher
                self.data_feeds['live_data'] = LiveDataFetcher({})
                logger.info("✅ [DATA] Live data fetcher initialized")
            except Exception as e:
                logger.warning(f"⚠️ [DATA] Live data fetcher initialization failed: {e}")
                
        except Exception as e:
            logger.error(f"❌ [DATA] Data feed initialization failed: {e}")
    
    async def run_trading_system(self):
        """Run the main trading system"""
        try:
            self.running = True
            logger.info("🎯 [TRADING] Starting continuous profit generation...")
            
            # Get the continuous trading engine
            continuous_engine = self.trading_engines.get('continuous')
            if not continuous_engine:
                logger.error("❌ [TRADING] No continuous trading engine available")
                return
            
            # Start the trading loop
            await continuous_engine.start_trading_loop()
            
        except KeyboardInterrupt:
            logger.info("🛑 [TRADING] Received stop signal")
        except Exception as e:
            logger.error(f"❌ [TRADING] Trading system error: {e}")
        finally:
            self.running = False
            logger.info("🏁 [TRADING] Trading system stopped")

async def main():
    """Main entry point"""
    try:
        # Initialize the trading system
        trader = AutoGPTTrader()
        
        # Initialize system components
        system_ready = await trader.initialize_system()
        
        if not system_ready:
            logger.error("❌ [STARTUP] System not ready for trading")
            return 1
        
        # Run the trading system
        await trader.run_trading_system()
        
        logger.info("✅ [SHUTDOWN] System shutdown complete")
        return 0
        
    except KeyboardInterrupt:
        logger.info("🛑 [SHUTDOWN] System shutdown by user")
        return 0
    except Exception as e:
        logger.error(f"❌ [CRITICAL] Fatal error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("[SHUTDOWN] System shutdown by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"[CRITICAL] Fatal error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
