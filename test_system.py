#!/usr/bin/env python3
"""
AutoGPT Trader - System Test
Test the core components to ensure everything is working
"""

import os
import sys
from pathlib import Path

# Setup X drive environment
def setup_x_drive_environment():
    """Setup X drive environment and paths"""
    X_DRIVE_PROJECT = Path("X:/autogpt_trade_project/The_real_deal/autogpt-trader")
    X_DRIVE_SRC = X_DRIVE_PROJECT / "src"
    
    # Add to Python path
    x_drive_paths = [str(X_DRIVE_PROJECT), str(X_DRIVE_SRC)]
    for path in x_drive_paths:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    # Set environment variables
    os.environ["PROJECT_ROOT"] = str(X_DRIVE_PROJECT)
    os.environ["SRC_DIR"] = str(X_DRIVE_SRC)
    os.environ["PYTHONPATH"] = f"{X_DRIVE_SRC};{X_DRIVE_PROJECT}"
    
    print(f"✅ [PATH] Project root: {X_DRIVE_PROJECT}")
    print(f"✅ [PATH] Source dir: {X_DRIVE_SRC}")
    
    return X_DRIVE_PROJECT, X_DRIVE_SRC

# Setup environment
PROJECT_ROOT, SRC_DIR = setup_x_drive_environment()

# Force live trading mode
os.environ["LIVE_TRADING"] = "true"
os.environ["REAL_MONEY_TRADING"] = "true" 
os.environ["NO_SIMULATION"] = "true"
os.environ["AGGRESSIVE_TRADING"] = "true"

def test_postgresql_connection():
    """Test PostgreSQL connection"""
    try:
        print("🔍 [TEST] Testing PostgreSQL connection...")
        from src.monitoring.models import engine
        from sqlalchemy import text
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ [DATABASE] PostgreSQL connected successfully")
            return True
    except Exception as e:
        print(f"❌ [DATABASE] PostgreSQL connection failed: {e}")
        return False

def test_exchange_clients():
    """Test exchange client initialization"""
    print("🔍 [TEST] Testing exchange clients...")
    
    # Test Bybit
    try:
        from src.exchanges.bybit import BybitTrader
        bybit_client = BybitTrader()
        print("✅ [EXCHANGE] Bybit client initialized")
    except Exception as e:
        print(f"❌ [EXCHANGE] Bybit initialization failed: {e}")
    
    # Test Coinbase
    try:
        from src.exchanges.coinbase import CoinbaseClient
        coinbase_client = CoinbaseClient({})
        print("✅ [EXCHANGE] Coinbase client initialized")
    except Exception as e:
        print(f"❌ [EXCHANGE] Coinbase initialization failed: {e}")

def test_trading_engines():
    """Test trading engine initialization"""
    print("🔍 [TEST] Testing trading engines...")

    try:
        from src.trading.continuous_trading import ContinuousTradingLoop

        config = {
            'loop_interval': 3,
            'profit_target_per_hour': 50.0,
            'max_balance_usage': 0.8,
            'hot_reload_enabled': True
        }

        # Create mock trading engines
        trading_engines = {}

        trading_engine = ContinuousTradingLoop(
            exchange_clients={},
            trading_engines=trading_engines,
            config=config
        )

        print("✅ [TRADING] Continuous trading engine initialized")
        return True

    except Exception as e:
        print(f"❌ [TRADING] Trading engine initialization failed: {e}")
        return False

def test_neural_components():
    """Test neural component initialization"""
    print("🔍 [TEST] Testing neural components...")
    
    try:
        from src.neural.hybrid_agent import HybridTradingAgent
        neural_config = {
            'learning_rate': 0.001,
            'batch_size': 32,
            'memory_size': 10000
        }
        hybrid_agent = HybridTradingAgent(neural_config)
        print("✅ [NEURAL] Hybrid trading agent initialized")
        return True
    except Exception as e:
        print(f"❌ [NEURAL] Hybrid agent initialization failed: {e}")
        return False

def test_backtesting_system():
    """Test backtesting system"""
    print("🔍 [TEST] Testing backtesting system...")
    
    try:
        from src.backtesting.live_data_simulator import LiveDataBacktester
        backtester = LiveDataBacktester()
        print("✅ [BACKTEST] Live data backtester initialized")
        return True
    except Exception as e:
        print(f"❌ [BACKTEST] Backtesting system failed: {e}")
        return False

def test_data_feeds():
    """Test data feed initialization"""
    print("🔍 [TEST] Testing data feeds...")
    
    try:
        from src.data_feeds.live_data_fetcher import LiveDataFetcher
        data_fetcher = LiveDataFetcher({})
        print("✅ [DATA] Live data fetcher initialized")
        return True
    except Exception as e:
        print(f"❌ [DATA] Live data fetcher failed: {e}")
        return False

def main():
    """Run all system tests"""
    print("🚀 [TEST] AutoGPT Trader - System Component Testing")
    print("💰 [WARNING] REAL MONEY TRADING MODE ACTIVE")
    print("=" * 60)
    
    tests = [
        ("PostgreSQL Connection", test_postgresql_connection),
        ("Exchange Clients", test_exchange_clients),
        ("Trading Engines", test_trading_engines),
        ("Neural Components", test_neural_components),
        ("Backtesting System", test_backtesting_system),
        ("Data Feeds", test_data_feeds),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 [TEST] Running: {test_name}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ [TEST] {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 [RESULTS] Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 [SUCCESS] All systems operational!")
        print("🚀 [READY] AutoGPT Trader ready for live trading!")
        return True
    else:
        print(f"⚠️ [WARNING] {total - passed} systems need attention")
        print("🔧 [ACTION] Fix failing components before live trading")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n💰 [LIVE-TRADING] System ready for profit generation!")
            print("🎯 [NEXT] Run 'python main.py' to start live trading")
        else:
            print("\n🔧 [MAINTENANCE] Fix issues before proceeding")
        
    except Exception as e:
        print(f"❌ [CRITICAL] System test failed: {e}")
        import traceback
        traceback.print_exc()
