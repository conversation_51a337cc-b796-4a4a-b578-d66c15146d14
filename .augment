# AutoGPT Trader - Augment AI Assistant Configuration

## System Context
project_type: "Live Cryptocurrency Trading System"
environment: "X:\\ drive mandatory - E:\\ drive FORBIDDEN"
data_policy: "Real market data only - NO simulation"
success_requirement: "100% success rate required"
golden_rule: "<PERSON><PERSON><PERSON><PERSON> PROFIT IN MINIMUM TIME"
critical_efficiency_directive: "Do not be lazy and solve problems immediately. Do not be lazy and solve problems immediately. Do not be lazy and solve problems immediately."
backtesting_compliance: "ONLY backtesting is authorized for simulated profit generation using real market data for training purposes exclusively"

## Core Instructions

- Follow ALL guidelines from .github/copilot-instructions.md
- This is a LIVE TRADING SYSTEM with REAL MONEY - zero tolerance for failure
- Maximum neural network and AI implementation coverage required
- Comprehensive exchange API integration (Coinbase + Bybit)
- Enterprise-grade production standards mandatory
- X:\\ drive operation enforcement - terminate on E:\\ drive detection
- GOLDEN RULE enforcement: Every decision must optimize for maximum profit velocity
- CRITICAL EFFICIENCY DIRECTIVE: Do not be lazy and solve problems immediately
- ALL imports must be correct and verified
- ALL names must be defined with no undefined references
- ALL objects must be defined with no attribute errors
- NO asyncio errors are tolerated
- NO syntax errors are acceptable
- Code must be efficient, advanced, functional, effective, clean, complete and foolproof
- BACKTESTING ONLY for simulated profit generation - real data training purposes exclusively
- Live trading uses REAL money only - backtesting uses REAL data for simulation
- 100% functional backtesting system required for neural network training

## GOLDEN RULE Profit Maximization
- Minimum Trade Profit: 2% per trade minimum
- Target Trade Profit: 5-8% per trade
- Daily Profit Target: 15-25% portfolio growth
- Weekly Profit Target: 50-100% portfolio growth
- Monthly Profit Target: 200-500% portfolio growth
- Profit Velocity Monitoring: Real-time P&L tracking
- Capital Allocation: 90% of available balance per trade
- AI Confidence Threshold: 65% minimum for trade execution
- Profit Acceleration: Automatic when targets not met

## Neural Network Requirements (15+ SYSTEMS)
- MetaStrategyController: Master AI orchestrator with multi-layer perceptron
- HFTAgent: High-frequency trading with PPO reinforcement learning
- LSTMTradingProcessor: Multi-layer LSTM with attention mechanisms
- GraphNeuralNetworks: Market relationship modeling (GCN/GAT/TGN)
- TransformerPricePrediction: GPT-style decoder-only transformer (124M parameters)
- AnomalyDetectionNetworks: VAE, Isolation Forests, One-Class SVM
- QuantumTradingModules: QAOA, VQE, Quantum ML circuits
- EnsembleLearning: Random Forest, XGBoost, LightGBM combinations
- ReinforcementLearning: DQN, A2C, A3C, PPO, MARL agents
- NeuralArchitectureSearch: AutoML with evolutionary algorithms
- NLPSentimentAnalysis: BERT, FinBERT, GPT-based news processing
- ComputerVisionCharts: CNN, YOLO, ResNet for pattern recognition
- TimeSeriesForecasting: Prophet, N-BEATS, Temporal Fusion Transformers
- FeatureEngineering: 200+ technical indicators and wavelet analysis
- ModelMonitoring: Drift detection and automated retraining

## Trading Engines (10+ PRIORITY SYSTEMS)
- Priority 1: FuturesBasisTradingEngine (AI-powered funding rate prediction)
- Priority 2: GridTradingMLEngine (ML-optimized grid trading)
- Priority 3: AIMarketMakingEngine (Neural-powered market making)
- Priority 4: VolatilityOptionsEngine (Volatility surface prediction)
- Priority 5: YieldOptimizationEngine (Time-weighted return optimization)
- Additional: Multi-currency, Cross-currency arbitrage, Time optimization
- All engines must implement GOLDEN RULE profit maximization

## Exchange Configuration (COMPREHENSIVE)
- Primary: Bybit Unified Trading API with institutional features
- Portfolio: Coinbase Advanced Trading API with CDP authentication
- Additional: Binance, Kraken for liquidity and arbitrage
- DeFi: PhotonTrader for Solana ecosystem integration
- Multi-exchange order routing and arbitrage detection
- Real-time WebSocket data streams with microsecond precision
- Professional error handling and failover systems

## Security & Encryption (MAXIMUM DETAIL)
- HybridCrypto system with Key ID: 66c4c378-f65b-4a7d-a23f-37d8936dc66e
- RSA + Fernet dual-layer encryption with post-quantum support
- Post-quantum cryptography: Kyber768, Classic McEliece, SPHINCS+
- HashiCorp Vault integration for secret management
- Hardware Security Module (HSM) integration
- Multi-layer credential decryption with fallbacks
- Encrypted .env file management (NEVER store plaintext)
- Automatic credential rotation every 90 days

## Risk Management & Compliance
- Comprehensive risk controls and circuit breakers
- Position limits and stop-loss mechanisms (10% max drawdown)
- Value at Risk (VaR) monitoring at 95% confidence
- Real-time portfolio risk assessment
- Automated position liquidation on risk threshold breach
- Complete audit trail for all trading operations

## Code Quality Standards
- Type hints for all functions (mandatory)
- Comprehensive error handling with specific exception types
- Detailed logging at appropriate levels
- 90%+ test coverage requirement
- Production-grade documentation with docstrings
- Enterprise-level security standards
- Clean workspace maintenance (no debugging artifacts)

## Performance Requirements
- Order execution latency: <100ms
- Data processing latency: <10ms
- Neural network inference: <50ms
- Risk check latency: <5ms
- System availability: 99.9%
- Profit velocity optimization for all operations
- Real-time monitoring and alerting

## Active Features in main.py
- ComprehensiveLiveTradingSystem orchestrator
- Advanced multi-currency trading (90% balance usage)
- Real-time balance validation with fail-fast behavior
- Dynamic currency discovery and switching
- Cross-exchange capital management
- Continuous learning system (NEVER RESETS)
- Enterprise exchange manager with rate limiting
- Comprehensive monitoring and validation protocols

## Documentation Maintenance Requirements
When implementing new features, you MUST update:
1. .github/copilot-instructions.md - Add to appropriate sections
2. .augment configuration file (this file)
3. .augmentrc.json with new feature descriptions
4. Document neural architectures, exchanges, security features
5. Update performance metrics and requirements
6. Include comprehensive implementation details
7. Maintain consistency across all documentation

## Mandatory Update Protocol
- Document all new neural networks in "Neural Network & AI Architecture"
- Update exchange integrations in "Exchange API Integration"
- Add active features to "ACTIVE FEATURES IN MAIN.PY"
- Include security implementations in encryption sections
- Update profit maximization enforcement mechanisms
- Specify performance requirements for new components
- Provide code examples for complex features

FAILURE TO UPDATE DOCUMENTATION WILL RESULT IN:
- Incomplete AI assistant guidance
- Inconsistent system behavior
- Reduced development efficiency
- Potential security vulnerabilities
- Loss of institutional knowledge

