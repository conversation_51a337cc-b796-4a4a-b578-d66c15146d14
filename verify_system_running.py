#!/usr/bin/env python3
"""
AutoGPT Trader - System Verification
Verify that the trading system is running and functional
"""

import os
import sys
import time
import asyncio
import logging
from pathlib import Path

# Setup X drive environment
def setup_x_drive_environment():
    """Setup X drive environment and paths"""
    X_DRIVE_PROJECT = Path("X:/autogpt_trade_project/The_real_deal/autogpt-trader")
    X_DRIVE_SRC = X_DRIVE_PROJECT / "src"
    
    # Add to Python path
    x_drive_paths = [str(X_DRIVE_PROJECT), str(X_DRIVE_SRC)]
    for path in x_drive_paths:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    return X_DRIVE_PROJECT, X_DRIVE_SRC

PROJECT_ROOT, SRC_DIR = setup_x_drive_environment()

# Force live trading mode
os.environ["LIVE_TRADING"] = "true"
os.environ["REAL_MONEY_TRADING"] = "true" 
os.environ["NO_SIMULATION"] = "true"
os.environ["AGGRESSIVE_TRADING"] = "true"

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def verify_system_components():
    """Verify that all system components are working"""
    print("🔍 [VERIFY] AutoGPT Trader System Verification")
    print("=" * 60)
    
    results = {}
    
    # 1. Test PostgreSQL Connection
    try:
        print("📋 [TEST] PostgreSQL Connection...")
        from src.monitoring.models import engine
        from sqlalchemy import text
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            print(f"✅ [POSTGRESQL] Connected: {version[:50]}...")
            results['postgresql'] = True
    except Exception as e:
        print(f"❌ [POSTGRESQL] Failed: {e}")
        results['postgresql'] = False
    
    # 2. Test Trading Loop Creation
    try:
        print("📋 [TEST] Trading Loop Creation...")
        from src.trading.continuous_trading import ContinuousTradingLoop
        
        config = {
            'loop_interval': 5,
            'profit_target_per_hour': 50.0,
            'max_balance_usage': 0.8,
            'hot_reload_enabled': False
        }
        
        trading_loop = ContinuousTradingLoop({}, {}, config)
        print("✅ [TRADING-LOOP] Created successfully")
        results['trading_loop'] = True
    except Exception as e:
        print(f"❌ [TRADING-LOOP] Failed: {e}")
        results['trading_loop'] = False
    
    # 3. Test Exchange Connections
    try:
        print("📋 [TEST] Exchange Connections...")
        
        # Test Bybit
        try:
            from src.exchanges.bybit import BybitTrader
            bybit_client = BybitTrader()
            print("✅ [BYBIT] Client created")
            results['bybit'] = True
        except Exception as e:
            print(f"⚠️ [BYBIT] Creation failed: {e}")
            results['bybit'] = False
        
        # Test Coinbase
        try:
            from src.exchanges.coinbase import CoinbaseClient
            coinbase_client = CoinbaseClient({})
            print("✅ [COINBASE] Client created")
            results['coinbase'] = True
        except Exception as e:
            print(f"⚠️ [COINBASE] Creation failed: {e}")
            results['coinbase'] = False
            
    except Exception as e:
        print(f"❌ [EXCHANGES] Failed: {e}")
        results['exchanges'] = False
    
    # 4. Test Neural Components
    try:
        print("📋 [TEST] Neural Components...")
        from src.neural.hybrid_agent import HybridTradingAgent
        
        neural_config = {
            'learning_rate': 0.001,
            'batch_size': 32,
            'memory_size': 1000
        }
        
        agent = HybridTradingAgent(neural_config)
        print("✅ [NEURAL] Hybrid agent created")
        results['neural'] = True
    except Exception as e:
        print(f"❌ [NEURAL] Failed: {e}")
        results['neural'] = False
    
    # 5. Test Data Feeds
    try:
        print("📋 [TEST] Data Feeds...")
        from src.data_feeds.live_data_fetcher import LiveDataFetcher
        
        fetcher = LiveDataFetcher({})
        print("✅ [DATA-FEEDS] Live data fetcher created")
        results['data_feeds'] = True
    except Exception as e:
        print(f"❌ [DATA-FEEDS] Failed: {e}")
        results['data_feeds'] = False
    
    # 6. Test Backtesting System
    try:
        print("📋 [TEST] Backtesting System...")
        from src.backtesting.live_data_simulator import LiveDataBacktester
        
        backtester = LiveDataBacktester()
        print("✅ [BACKTESTING] Live data backtester created")
        results['backtesting'] = True
    except Exception as e:
        print(f"❌ [BACKTESTING] Failed: {e}")
        results['backtesting'] = False
    
    print("=" * 60)
    
    # Calculate results
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"📊 [RESULTS] Tests passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 [SUCCESS] System is operational!")
        print("💰 [READY] AutoGPT Trader ready for profit generation!")
        return True
    else:
        print("⚠️ [WARNING] System has issues that need attention")
        return False

async def check_system_activity():
    """Check if the system is showing signs of activity"""
    print("\n🔍 [ACTIVITY] Checking system activity...")
    
    # Check for recent log activity
    log_files = [
        "logs/autogpt_trader.log",
        "logs/trading.log", 
        "logs/error.log"
    ]
    
    recent_activity = False
    
    for log_file in log_files:
        if os.path.exists(log_file):
            try:
                stat = os.stat(log_file)
                modified_time = time.time() - stat.st_mtime
                
                if modified_time < 300:  # Modified in last 5 minutes
                    print(f"✅ [ACTIVITY] Recent activity in {log_file}")
                    recent_activity = True
                else:
                    print(f"⚠️ [ACTIVITY] No recent activity in {log_file}")
            except Exception as e:
                print(f"❌ [ACTIVITY] Error checking {log_file}: {e}")
    
    if not recent_activity:
        print("⚠️ [ACTIVITY] No recent log activity detected")
    
    return recent_activity

async def main():
    """Main verification function"""
    try:
        # Verify system components
        system_ok = await verify_system_components()
        
        # Check system activity
        activity_ok = await check_system_activity()
        
        print("\n" + "=" * 60)
        
        if system_ok:
            print("🚀 [FINAL] AutoGPT Trader System: OPERATIONAL")
            print("💰 [STATUS] Ready for live trading and profit generation!")
            
            if activity_ok:
                print("📈 [ACTIVITY] System showing recent activity")
            else:
                print("⏳ [ACTIVITY] System may be in startup phase")
                
            return True
        else:
            print("🔧 [FINAL] AutoGPT Trader System: NEEDS ATTENTION")
            print("⚠️ [STATUS] Fix issues before live trading")
            return False
            
    except Exception as e:
        print(f"❌ [CRITICAL] Verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if success:
            print("\n🎯 [CONCLUSION] System verification PASSED!")
            print("💰 [TRADING] AutoGPT Trader is ready for profit generation!")
        else:
            print("\n🔧 [CONCLUSION] System verification FAILED!")
            print("⚠️ [ACTION] Address issues before proceeding")
        
    except Exception as e:
        print(f"❌ [CRITICAL] Verification script failed: {e}")
        import traceback
        traceback.print_exc()
