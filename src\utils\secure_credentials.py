#!/usr/bin/env python3
"""
Secure Credential Management System
Encrypts and decrypts API credentials for secure storage
"""

import os
import base64
import logging
import sys
from pathlib import Path

# Avoid naming conflict with local cryptography module
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
except ImportError as e:
    print(f"❌ [CRYPTO] Failed to import cryptography: {e}")
    print("Please install: pip install cryptography")
    sys.exit(1)

logger = logging.getLogger(__name__)

class SecureCredentials:
    """Secure credential encryption/decryption system"""
    
    def __init__(self):
        self.key = self._get_or_create_key()
        self.fernet = Fernet(self.key)
        
    def _get_or_create_key(self) -> bytes:
        """Get or create encryption key"""
        key_file = Path("data/encryption.key")
        
        # Create data directory if it doesn't exist
        key_file.parent.mkdir(exist_ok=True)
        
        if key_file.exists():
            # Load existing key
            with open(key_file, 'rb') as f:
                key = f.read()
            logger.info("🔑 [CRYPTO] Loaded existing encryption key")
        else:
            # Generate new key
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            logger.info("🔑 [CRYPTO] Generated new encryption key")
            
        return key
    
    def encrypt_value(self, value: str) -> str:
        """Encrypt a credential value"""
        if not value:
            return ""
            
        encrypted = self.fernet.encrypt(value.encode())
        encoded = base64.b64encode(encrypted).decode()
        logger.debug(f"🔒 [CRYPTO] Encrypted credential (length: {len(value)})")
        return encoded
    
    def decrypt_value(self, encrypted_value: str) -> str:
        """Decrypt a credential value"""
        if not encrypted_value:
            return ""
            
        try:
            decoded = base64.b64decode(encrypted_value.encode())
            decrypted = self.fernet.decrypt(decoded).decode()
            logger.debug(f"🔓 [CRYPTO] Decrypted credential (length: {len(decrypted)})")
            return decrypted
        except Exception as e:
            logger.error(f"❌ [CRYPTO] Failed to decrypt credential: {e}")
            raise ValueError(f"Failed to decrypt credential: {e}")

# Global instance
_secure_credentials = None

def get_secure_credentials() -> SecureCredentials:
    """Get global secure credentials instance"""
    global _secure_credentials
    if _secure_credentials is None:
        _secure_credentials = SecureCredentials()
    return _secure_credentials

def encrypt_value(value: str) -> str:
    """Encrypt a value using the global instance"""
    return get_secure_credentials().encrypt_value(value)

def decrypt_value(encrypted_value: str) -> str:
    """Decrypt a value using the global instance"""
    return get_secure_credentials().decrypt_value(encrypted_value)

def encrypt_credentials_in_env():
    """Encrypt all plain text credentials in .env file"""
    env_path = Path(".env")
    if not env_path.exists():
        logger.error("❌ [CRYPTO] .env file not found")
        return False
    
    # Read current .env content
    with open(env_path, 'r') as f:
        lines = f.readlines()
    
    secure_creds = get_secure_credentials()
    updated_lines = []
    changes_made = False
    
    for line in lines:
        line = line.strip()
        
        # Skip comments and empty lines
        if not line or line.startswith('#'):
            updated_lines.append(line + '\n')
            continue
            
        # Check for credentials that need encryption
        if '=' in line:
            key, value = line.split('=', 1)
            key = key.strip()
            value = value.strip().strip('"')
            
            # Encrypt specific credential fields
            if key in ['BYBIT_API_KEY', 'BYBIT_API_SECRET', 'COINBASE_API_KEY_NAME_SAFE', 'COINBASE_PRIVATE_KEY_SAFE']:
                if not value.startswith('ENCRYPTED_'):
                    # Encrypt the value
                    encrypted_value = secure_creds.encrypt_value(value)
                    updated_lines.append(f"ENCRYPTED_{key}={encrypted_value}\n")
                    changes_made = True
                    logger.info(f"🔒 [CRYPTO] Encrypted {key}")
                else:
                    updated_lines.append(line + '\n')
            else:
                updated_lines.append(line + '\n')
        else:
            updated_lines.append(line + '\n')
    
    if changes_made:
        # Backup original file
        backup_path = env_path.with_suffix('.env.backup')
        with open(backup_path, 'w') as f:
            with open(env_path, 'r') as original:
                f.write(original.read())
        
        # Write updated file
        with open(env_path, 'w') as f:
            f.writelines(updated_lines)
        
        logger.info(f"✅ [CRYPTO] Credentials encrypted and saved to {env_path}")
        logger.info(f"📁 [CRYPTO] Original backed up to {backup_path}")
        return True
    else:
        logger.info("ℹ️ [CRYPTO] No credentials needed encryption")
        return False

def get_decrypted_credential(env_var_name: str) -> str:
    """Get a decrypted credential from environment"""
    # Try encrypted version first
    encrypted_var = f"ENCRYPTED_{env_var_name}"
    encrypted_value = os.getenv(encrypted_var)
    
    if encrypted_value:
        try:
            return decrypt_value(encrypted_value)
        except Exception as e:
            logger.warning(f"⚠️ [CRYPTO] Failed to decrypt {encrypted_var}: {e}")
    
    # Fallback to plain version (for backwards compatibility)
    plain_value = os.getenv(env_var_name)
    if plain_value:
        logger.warning(f"⚠️ [CRYPTO] Using plain text credential {env_var_name} - should be encrypted!")
        return plain_value
    
    raise ValueError(f"Credential {env_var_name} not found in environment (tried {encrypted_var} and {env_var_name})")

if __name__ == "__main__":
    # Command line tool to encrypt credentials
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "encrypt":
        print("🔒 [CRYPTO] Encrypting credentials in .env file...")
        success = encrypt_credentials_in_env()
        if success:
            print("✅ [CRYPTO] Credentials encrypted successfully!")
        else:
            print("ℹ️ [CRYPTO] No changes needed")
    else:
        print("Usage: python secure_credentials.py encrypt")
