#!/usr/bin/env python3
"""
Test main.py credential loading exactly as main.py does it
"""

import os
import sys
from pathlib import Path

# Setup X drive environment
def setup_x_drive_environment():
    """Setup X drive environment and paths"""
    X_DRIVE_PROJECT = Path("X:/autogpt_trade_project/The_real_deal/autogpt-trader")
    X_DRIVE_SRC = X_DRIVE_PROJECT / "src"
    
    # Add to Python path
    x_drive_paths = [str(X_DRIVE_PROJECT), str(X_DRIVE_SRC)]
    for path in x_drive_paths:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    return X_DRIVE_PROJECT, X_DRIVE_SRC

PROJECT_ROOT, SRC_DIR = setup_x_drive_environment()

def test_credential_loading():
    """Test credential loading exactly as main.py does"""
    print("🔍 [TEST] Testing credential loading as main.py does...")
    
    try:
        # Import the decryption function
        from src.utils.cryptography.secure_credentials import decrypt_value
        print("✅ [IMPORT] Decryption function imported successfully")
    except ImportError:
        try:
            from src.utils.secure_credentials import decrypt_value
            print("✅ [IMPORT] Fallback decryption function imported successfully")
        except ImportError as e:
            print(f"❌ [IMPORT] Failed to import decryption function: {e}")
            return False
    
    # Test decryption of encrypted credentials
    print("\n🔐 [DECRYPT] Testing credential decryption...")
    
    # Test Coinbase credentials
    coinbase_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
    coinbase_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
    
    if coinbase_api_key:
        try:
            decrypted_key = decrypt_value(coinbase_api_key)
            if decrypted_key:
                os.environ['COINBASE_API_KEY_NAME'] = decrypted_key
                print(f"✅ [COINBASE] API key decrypted: {decrypted_key[:50]}...")
            else:
                print("❌ [COINBASE] API key decryption returned None")
                return False
        except Exception as e:
            print(f"❌ [COINBASE] API key decryption failed: {e}")
            return False
    else:
        print("❌ [COINBASE] ENCRYPTED_COINBASE_API_KEY_NAME not found in environment")
        return False
    
    if coinbase_private_key:
        try:
            decrypted_private_key = decrypt_value(coinbase_private_key)
            if decrypted_private_key:
                os.environ['COINBASE_PRIVATE_KEY'] = decrypted_private_key
                print("✅ [COINBASE] Private key decrypted successfully")
            else:
                print("❌ [COINBASE] Private key decryption returned None")
                return False
        except Exception as e:
            print(f"❌ [COINBASE] Private key decryption failed: {e}")
            return False
    else:
        print("❌ [COINBASE] ENCRYPTED_COINBASE_PRIVATE_KEY not found in environment")
        return False
    
    # Test Bybit credentials
    bybit_api_key = os.getenv('ENCRYPTED_BYBIT_API_KEY')
    bybit_api_secret = os.getenv('ENCRYPTED_BYBIT_API_SECRET')
    
    if bybit_api_key:
        try:
            decrypted_key = decrypt_value(bybit_api_key)
            if decrypted_key:
                os.environ['BYBIT_API_KEY'] = decrypted_key
                print(f"✅ [BYBIT] API key decrypted: {decrypted_key}")
            else:
                print("❌ [BYBIT] API key decryption returned None")
                return False
        except Exception as e:
            print(f"❌ [BYBIT] API key decryption failed: {e}")
            return False
    else:
        print("❌ [BYBIT] ENCRYPTED_BYBIT_API_KEY not found in environment")
        return False
    
    if bybit_api_secret:
        try:
            decrypted_secret = decrypt_value(bybit_api_secret)
            if decrypted_secret:
                os.environ['BYBIT_API_SECRET'] = decrypted_secret
                print(f"✅ [BYBIT] API secret decrypted: {decrypted_secret[:20]}...")
            else:
                print("❌ [BYBIT] API secret decryption returned None")
                return False
        except Exception as e:
            print(f"❌ [BYBIT] API secret decryption failed: {e}")
            return False
    else:
        print("❌ [BYBIT] ENCRYPTED_BYBIT_API_SECRET not found in environment")
        return False
    
    print("\n✅ [SUCCESS] All credentials decrypted successfully!")
    return True

def test_credential_validation():
    """Test credential validation as main.py does"""
    print("\n🔍 [VALIDATE] Testing credential validation...")
    
    # Check required environment variables
    required_env_vars = [
        'COINBASE_API_KEY_NAME', 'COINBASE_PRIVATE_KEY',
        'BYBIT_API_KEY', 'BYBIT_API_SECRET'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ [VALIDATE] Missing required environment variables: {missing_vars}")
        return False
    
    # Validate Coinbase CDP credentials
    coinbase_key = os.getenv('COINBASE_API_KEY_NAME')
    coinbase_secret = os.getenv('COINBASE_PRIVATE_KEY')
    
    if not coinbase_key or not coinbase_key.startswith("organizations/"):
        print(f"❌ [VALIDATE] Coinbase API key must be in CDP format (organizations/...): {coinbase_key}")
        return False
    
    if not coinbase_secret or not coinbase_secret.startswith("-----BEGIN"):
        print(f"❌ [VALIDATE] Coinbase private key must be in PEM format: {coinbase_secret[:50]}...")
        return False
    
    # Validate Bybit credentials
    bybit_key = os.getenv('BYBIT_API_KEY')
    bybit_secret = os.getenv('BYBIT_API_SECRET')
    
    if not bybit_key or len(bybit_key) < 10:
        print(f"❌ [VALIDATE] Bybit API key invalid: {bybit_key}")
        return False
    
    if not bybit_secret or len(bybit_secret) < 20:
        print(f"❌ [VALIDATE] Bybit API secret invalid: {bybit_secret[:20]}...")
        return False
    
    print("✅ [VALIDATE] All credentials validated successfully!")
    return True

def main():
    """Main test function"""
    print("🚀 [TEST] Testing main.py credential system...")
    print("=" * 60)
    
    # Test credential loading
    if not test_credential_loading():
        print("\n❌ [FAILED] Credential loading test failed!")
        return False
    
    # Test credential validation
    if not test_credential_validation():
        print("\n❌ [FAILED] Credential validation test failed!")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 [SUCCESS] All credential tests passed!")
    print("💰 [READY] Credentials are ready for main.py!")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ [CONCLUSION] Credential system working perfectly!")
        else:
            print("\n❌ [CONCLUSION] Credential system has issues!")
        
    except Exception as e:
        print(f"❌ [CRITICAL] Test failed: {e}")
        import traceback
        traceback.print_exc()
