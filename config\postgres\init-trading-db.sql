-- PostgreSQL Initialization for AutoGPT Trader
-- CRITICAL: This database is essential for persistent learning and trade tracking

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create primary trading tables
CREATE TABLE IF NOT EXISTS trades (
    id SERIAL PRIMARY KEY,
    trade_id UUID DEFAULT uuid_generate_v4() UNIQUE,
    exchange VARCHAR(50) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    side VARCHAR(4) NOT NULL CHECK (side IN ('buy', 'sell')),
    quantity DECIMAL(18,8) NOT NULL,
    price DECIMAL(18,8) NOT NULL,
    filled_quantity DECIMAL(18,8) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending',
    strategy VARCHAR(100),
    ai_confidence DECIMAL(5,4),
    expected_profit DECIMAL(18,8),
    actual_profit DECIMAL(18,8),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Neural network learning data
CREATE TABLE IF NOT EXISTS neural_learning_data (
    id SERIAL PRIMARY KEY,
    session_id UUID DEFAULT uuid_generate_v4(),
    model_name VARCHAR(100) NOT NULL,
    input_features JSONB,
    prediction JSONB,
    actual_result JSONB,
    accuracy_score DECIMAL(8,6),
    loss_value DECIMAL(12,8),
    learning_rate DECIMAL(8,6),
    epoch_number INTEGER,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Strategy performance tracking
CREATE TABLE IF NOT EXISTS strategy_performance (
    id SERIAL PRIMARY KEY,
    strategy_name VARCHAR(100) NOT NULL,
    total_trades INTEGER DEFAULT 0,
    winning_trades INTEGER DEFAULT 0,
    total_profit DECIMAL(18,8) DEFAULT 0,
    max_drawdown DECIMAL(8,4) DEFAULT 0,
    sharpe_ratio DECIMAL(8,4),
    win_rate DECIMAL(5,4),
    avg_profit_per_trade DECIMAL(18,8),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Market data for backtesting and analysis
CREATE TABLE IF NOT EXISTS market_data (
    id SERIAL PRIMARY KEY,
    exchange VARCHAR(50) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    open_price DECIMAL(18,8),
    high_price DECIMAL(18,8),
    low_price DECIMAL(18,8),
    close_price DECIMAL(18,8),
    volume DECIMAL(20,8),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance metrics
CREATE TABLE IF NOT EXISTS performance_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(18,8),
    metric_type VARCHAR(50), -- 'profit', 'risk', 'efficiency', etc.
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    additional_data JSONB
);

-- Risk management logs
CREATE TABLE IF NOT EXISTS risk_logs (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    message TEXT,
    trade_id UUID REFERENCES trades(trade_id),
    action_taken VARCHAR(100),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_trades_symbol_created ON trades(symbol, created_at);
CREATE INDEX IF NOT EXISTS idx_trades_strategy_created ON trades(strategy, created_at);
CREATE INDEX IF NOT EXISTS idx_trades_status ON trades(status);
CREATE INDEX IF NOT EXISTS idx_neural_learning_model_timestamp ON neural_learning_data(model_name, timestamp);
CREATE INDEX IF NOT EXISTS idx_market_data_symbol_timestamp ON market_data(symbol, timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_name_timestamp ON performance_metrics(metric_name, timestamp);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_trades_updated_at BEFORE UPDATE ON trades 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_strategy_performance_updated_at BEFORE UPDATE ON strategy_performance 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert initial strategy performance records
INSERT INTO strategy_performance (strategy_name, total_trades, winning_trades, total_profit, win_rate)
VALUES 
    ('FuturesBasisTrading', 0, 0, 0, 0),
    ('GridTradingML', 0, 0, 0, 0),
    ('AIMarketMaking', 0, 0, 0, 0),
    ('VolatilityOptions', 0, 0, 0, 0),
    ('YieldOptimization', 0, 0, 0, 0),
    ('ArbitrageEngine', 0, 0, 0, 0),
    ('NeuralTrendFollowing', 0, 0, 0, 0)
ON CONFLICT DO NOTHING;

-- Grant permissions to the trader user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO trader;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO trader;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO trader;

-- Set default permissions for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO trader;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO trader;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO trader;

-- Database optimization settings
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET track_activity_query_size = 2048;
ALTER SYSTEM SET pg_stat_statements.track = 'all';
