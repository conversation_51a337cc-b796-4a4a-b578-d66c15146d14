"""
Secure Credentials Management for Exchange APIs
Provides encryption/decryption for sensitive API credentials
"""

import os
import base64
import logging
from typing import Optional, Union
from cryptography.fernet import Ferne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger(__name__)

class SecureCredentials:
    """Secure credentials manager with encryption/decryption capabilities"""
    
    def __init__(self, password: Optional[str] = None):
        """Initialize with optional password for encryption"""
        self.password = password or os.getenv('CREDENTIALS_PASSWORD', 'default_password')
        self._fernet = None
    
    def _get_fernet(self) -> Ferne<PERSON>:
        """Get or create Fernet encryption instance"""
        if self._fernet is None:
            try:
                # First try to load the key from our main encryption key file
                key_file_path = "data/encryption.key"
                if os.path.exists(key_file_path):
                    with open(key_file_path, 'rb') as key_file:
                        key = key_file.read()
                    self._fernet = Fernet(key)
                    logger.info("🔐 [CRYPTO] Using main encryption key from data/encryption.key")
                else:
                    # Try the old fernet.key file location
                    old_key_file_path = os.path.join(os.path.dirname(__file__), 'fernet.key')
                    if os.path.exists(old_key_file_path):
                        with open(old_key_file_path, 'rb') as key_file:
                            key = key_file.read()
                        self._fernet = Fernet(key)
                        logger.info("🔐 [CRYPTO] Using Fernet key from old location")
                    else:
                        # Generate new key and save it
                        key = Fernet.generate_key()
                        os.makedirs("data", exist_ok=True)
                        with open(key_file_path, 'wb') as key_file:
                            key_file.write(key)
                        self._fernet = Fernet(key)
                        logger.info("🔐 [CRYPTO] Generated new encryption key")
            except Exception as e:
                logger.error(f"❌ [CRYPTO] Failed to initialize Fernet: {e}")
                # Create a fallback key
                key = Fernet.generate_key()
                self._fernet = Fernet(key)
        
        return self._fernet
    
    def encrypt_value(self, value: str) -> str:
        """Encrypt a string value"""
        try:
            fernet = self._get_fernet()
            encrypted_bytes = fernet.encrypt(value.encode())
            return base64.urlsafe_b64encode(encrypted_bytes).decode()
        except Exception as e:
            logger.error(f"❌ [CRYPTO] Encryption failed: {e}")
            return value  # Return original value if encryption fails
    
    def decrypt_value(self, encrypted_value: str) -> str:
        """Decrypt an encrypted string value, compatible with main secure_credentials"""
        try:
            if not encrypted_value:
                return ""

            fernet = self._get_fernet()

            # Use the same decryption method as our main secure_credentials
            try:
                decoded = base64.b64decode(encrypted_value.encode())
                decrypted = fernet.decrypt(decoded).decode()
                logger.debug(f"🔓 [CRYPTO] Decrypted credential (length: {len(decrypted)})")
                return decrypted
            except Exception as decrypt_error:
                logger.warning(f"⚠️ [CRYPTO] Standard decryption failed: {decrypt_error}")
                # Try the old method as fallback
                try:
                    encrypted_bytes = base64.urlsafe_b64decode(encrypted_value.encode())
                    decrypted_bytes = fernet.decrypt(encrypted_bytes)
                    result = decrypted_bytes.decode()
                    logger.info("✅ [CRYPTO] Fallback decryption successful")
                    return result
                except Exception as fallback_error:
                    logger.error(f"❌ [CRYPTO] Both decryption methods failed: {fallback_error}")
                    raise ValueError(f"Failed to decrypt credential: {fallback_error}")

        except Exception as e:
            logger.error(f"❌ [CRYPTO] Critical decryption error: {e}")
            return None  # Return None to indicate decryption failure

# Global instance for easy access
_secure_credentials = SecureCredentials()

def encrypt_value(value: str) -> str:
    """Encrypt a string value using global instance"""
    return _secure_credentials.encrypt_value(value)

def decrypt_value(encrypted_value: str) -> str:
    """Decrypt an encrypted string value using global instance"""
    return _secure_credentials.decrypt_value(encrypted_value)

def get_secure_credential(key: str, default: Optional[str] = None) -> Optional[str]:
    """Get and decrypt a credential from environment variables"""
    encrypted_value = os.getenv(key, default)
    if encrypted_value:
        return decrypt_value(encrypted_value)
    return None

# Backward compatibility functions
def load_encrypted_credential(key: str) -> Optional[str]:
    """Load and decrypt credential from environment"""
    return get_secure_credential(key)

def save_encrypted_credential(key: str, value: str) -> bool:
    """Encrypt and save credential (for setup scripts)"""
    try:
        encrypted_value = encrypt_value(value)
        # In a real implementation, this would save to a secure store
        logger.info(f"✅ [CRYPTO] Credential {key} encrypted successfully")
        logger.info(f"Set environment variable: {key}={encrypted_value}")
        return True
    except Exception as e:
        logger.error(f"❌ [CRYPTO] Failed to save credential {key}: {e}")
        return False

# Global instance for compatibility
_secure_credentials = SecureCredentials()

# Main.py compatibility functions
def decrypt_value(encrypted_value: str) -> str:
    """Decrypt a value - main.py compatibility function"""
    return _secure_credentials.decrypt_value(encrypted_value)
