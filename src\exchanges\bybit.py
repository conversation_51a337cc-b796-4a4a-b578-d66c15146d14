# src/exchanges/bybit.py
import os
import logging
from pybit.unified_trading import HTTP

logger = logging.getLogger(__name__)

class BybitTrader:
    def __init__(self):
        # Get credentials directly from environment
        api_key = os.getenv('BYBIT_API_KEY')
        api_secret = os.getenv('BYBIT_API_SECRET')

        if not api_key or not api_secret:
            raise ValueError("Missing Bybit credentials in environment variables. Please set BYBIT_API_KEY and BYBIT_API_SECRET")

        # Check if testnet should be used
        testnet = os.getenv('BYBIT_TESTNET', 'false').lower() == 'true'

        logger.info(f"🔗 [BYBIT] Initializing Bybit client (testnet: {testnet})")

        self.session = HTTP(
            api_key=api_key,
            api_secret=api_secret,
            testnet=testnet
        )

        logger.info("✅ [BYBIT] Bybit client initialized successfully")

    def get_balance(self, coin: str = "USDT") -> dict:
        """Retrieve wallet balance"""
        try:
            response = self.session.get_wallet_balance(accountType="UNIFIED", coin=coin)
            return response['result']['list'][0]['coin'][0]['walletBalance']
        except Exception as e:
            raise TradingError(f"Bybit balance check failed: {str(e)}")