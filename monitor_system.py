#!/usr/bin/env python3
"""
AutoGPT Trader - System Monitor
Monitor the running trading system for activity and performance
"""

import os
import sys
import time
import psutil
import sqlite3
from pathlib import Path
from datetime import datetime

# Setup X drive environment
def setup_x_drive_environment():
    """Setup X drive environment and paths"""
    X_DRIVE_PROJECT = Path("X:/autogpt_trade_project/The_real_deal/autogpt-trader")
    X_DRIVE_SRC = X_DRIVE_PROJECT / "src"
    
    # Add to Python path
    x_drive_paths = [str(X_DRIVE_PROJECT), str(X_DRIVE_SRC)]
    for path in x_drive_paths:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    return X_DRIVE_PROJECT, X_DRIVE_SRC

PROJECT_ROOT, SRC_DIR = setup_x_drive_environment()

def check_main_py_process():
    """Check if main.py is running"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] == 'python.exe' and proc.info['cmdline']:
                cmdline = ' '.join(proc.info['cmdline'])
                if 'main.py' in cmdline:
                    return {
                        'pid': proc.info['pid'],
                        'status': 'running',
                        'memory_mb': proc.memory_info().rss / 1024 / 1024,
                        'cpu_percent': proc.cpu_percent(),
                        'create_time': datetime.fromtimestamp(proc.create_time())
                    }
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def check_postgresql_connection():
    """Check PostgreSQL connection"""
    try:
        from src.monitoring.models import engine
        from sqlalchemy import text
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            return True
    except Exception as e:
        return False

def check_database_activity():
    """Check for recent database activity"""
    try:
        # Check if there are any recent log entries
        db_path = "data/trading_monitor.db"
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check for recent entries (last 5 minutes)
            cursor.execute("""
                SELECT COUNT(*) FROM sqlite_master 
                WHERE type='table' AND name='trading_sessions'
            """)
            
            if cursor.fetchone()[0] > 0:
                cursor.execute("""
                    SELECT COUNT(*) FROM trading_sessions 
                    WHERE created_at > datetime('now', '-5 minutes')
                """)
                recent_count = cursor.fetchone()[0]
                conn.close()
                return recent_count
            
            conn.close()
        return 0
    except Exception as e:
        return 0

def check_log_files():
    """Check for recent log activity"""
    log_files = [
        "logs/autogpt_trader.log",
        "logs/trading.log", 
        "logs/error.log"
    ]
    
    recent_activity = {}
    
    for log_file in log_files:
        if os.path.exists(log_file):
            try:
                stat = os.stat(log_file)
                modified_time = datetime.fromtimestamp(stat.st_mtime)
                size_mb = stat.st_size / 1024 / 1024
                
                # Check if modified in last 5 minutes
                time_diff = datetime.now() - modified_time
                is_recent = time_diff.total_seconds() < 300  # 5 minutes
                
                recent_activity[log_file] = {
                    'size_mb': round(size_mb, 2),
                    'modified': modified_time,
                    'recent_activity': is_recent
                }
            except Exception as e:
                recent_activity[log_file] = {'error': str(e)}
    
    return recent_activity

def check_docker_containers():
    """Check Docker containers status"""
    try:
        import subprocess
        result = subprocess.run(['docker', 'ps', '--format', 'table {{.Names}}\t{{.Status}}'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            containers = {}
            for line in lines:
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        name = parts[0]
                        status = parts[1]
                        containers[name] = status
            return containers
        return {}
    except Exception as e:
        return {'error': str(e)}

def monitor_system():
    """Monitor the trading system"""
    print("🔍 [MONITOR] AutoGPT Trader System Monitor")
    print("=" * 60)
    
    # Check main.py process
    main_process = check_main_py_process()
    if main_process:
        print(f"✅ [PROCESS] main.py running (PID: {main_process['pid']})")
        print(f"   📊 Memory: {main_process['memory_mb']:.1f} MB")
        print(f"   ⚡ CPU: {main_process['cpu_percent']:.1f}%")
        print(f"   🕐 Started: {main_process['create_time']}")
    else:
        print("❌ [PROCESS] main.py not running")
    
    # Check PostgreSQL
    pg_status = check_postgresql_connection()
    if pg_status:
        print("✅ [DATABASE] PostgreSQL connected")
    else:
        print("❌ [DATABASE] PostgreSQL connection failed")
    
    # Check Docker containers
    containers = check_docker_containers()
    if containers and not containers.get('error'):
        print("✅ [DOCKER] Containers status:")
        for name, status in containers.items():
            print(f"   📦 {name}: {status}")
    elif containers.get('error'):
        print(f"⚠️ [DOCKER] Error checking containers: {containers['error']}")
    else:
        print("❌ [DOCKER] No containers running")
    
    # Check database activity
    db_activity = check_database_activity()
    print(f"📊 [DATABASE] Recent activity: {db_activity} entries (last 5 min)")
    
    # Check log files
    log_activity = check_log_files()
    if log_activity:
        print("📝 [LOGS] Log file status:")
        for log_file, info in log_activity.items():
            if 'error' in info:
                print(f"   ❌ {log_file}: {info['error']}")
            else:
                status = "🟢" if info['recent_activity'] else "🔴"
                print(f"   {status} {log_file}: {info['size_mb']} MB, modified: {info['modified']}")
    else:
        print("❌ [LOGS] No log files found")
    
    print("=" * 60)
    
    # Overall system status
    if main_process and pg_status:
        print("🎉 [STATUS] System appears to be running normally!")
        print("💰 [TRADING] AutoGPT Trader is active for profit generation")
        
        # Calculate uptime
        uptime = datetime.now() - main_process['create_time']
        hours = uptime.total_seconds() / 3600
        print(f"⏱️ [UPTIME] System running for {hours:.1f} hours")
        
    else:
        print("⚠️ [STATUS] System issues detected")
        if not main_process:
            print("🔧 [ACTION] Start main.py to begin trading")
        if not pg_status:
            print("🔧 [ACTION] Check PostgreSQL connection")

def continuous_monitor():
    """Run continuous monitoring"""
    print("🚀 [MONITOR] Starting continuous system monitoring...")
    print("Press Ctrl+C to stop monitoring\n")
    
    try:
        while True:
            monitor_system()
            print(f"\n⏳ [MONITOR] Next check in 60 seconds...\n")
            time.sleep(60)
    except KeyboardInterrupt:
        print("\n🛑 [MONITOR] Monitoring stopped by user")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--continuous":
        continuous_monitor()
    else:
        monitor_system()
        print("\n💡 [TIP] Use 'python monitor_system.py --continuous' for continuous monitoring")
