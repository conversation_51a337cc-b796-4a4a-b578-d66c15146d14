# src/vault_client.py
import os
import logging

logger = logging.getLogger(__name__)

class VaultClient:
    def __init__(self):
        self.vault_available = False
        self.client = None
        self.crypto = None

        # Try to initialize Vault if available
        try:
            import hvac
            from src.utils.cryptpography.hybrid import HybridCrypto

            vault_url = os.getenv('VAULT_URL')
            vault_token = os.getenv('VAULT_TOKEN')

            if vault_url and vault_token:
                self.client = hvac.Client(
                    url=vault_url,
                    token=vault_token,
                    cert=(
                        'src/utils/cryptography/vault.crt',
                        'src/utils/cryptography/vault.key'
                    ),
                    verify='src/utils/cryptography/ca.crt'
                )
                self.crypto = HybridCrypto('src/utils/cryptography/private.pem')

                if self.client.is_authenticated():
                    self.vault_available = True
                    logger.info("✅ [VAULT] Vault client authenticated successfully")
                else:
                    logger.warning("⚠️ [VAULT] Vault authentication failed, using environment fallback")
            else:
                logger.info("ℹ️ [VAULT] Vault not configured, using environment variables")

        except Exception as e:
            logger.warning(f"⚠️ [VAULT] Vault initialization failed: {e}, using environment fallback")

    def get_exchange_secret(self, exchange: str) -> dict:
        """Retrieve exchange credentials from Vault or environment variables"""
        if self.vault_available:
            try:
                # Try Vault first
                secret_path = f"trading/exchanges/{exchange}"
                encrypted = self.client.secrets.kv.v2.read_secret_version(
                    path=secret_path
                )['data']['data']

                return {
                    'api_key': self.crypto.decrypt_value(encrypted['api_key']),
                    'api_secret': self.crypto.decrypt_value(encrypted['api_secret'])
                }
            except Exception as e:
                logger.warning(f"⚠️ [VAULT] Failed to get {exchange} credentials from Vault: {e}")

        # Fallback to environment variables
        return self._get_credentials_from_env(exchange)

    def _get_credentials_from_env(self, exchange: str) -> dict:
        """Get credentials from environment variables"""
        logger.info(f"🔑 [ENV] Loading {exchange} credentials from environment")

        if exchange.lower() == 'bybit':
            api_key = os.getenv('BYBIT_API_KEY')
            api_secret = os.getenv('BYBIT_API_SECRET')

            if not api_key or not api_secret:
                raise ValueError(f"Missing Bybit credentials in environment variables")

            return {
                'api_key': api_key,
                'api_secret': api_secret
            }

        elif exchange.lower() == 'coinbase':
            # Try multiple credential formats
            api_key = (os.getenv('COINBASE_API_KEY') or
                      os.getenv('COINBASE_API_KEY_NAME') or
                      os.getenv('COINBASE_API_KEY_NAME_SAFE'))

            api_secret = (os.getenv('COINBASE_API_SECRET') or
                         os.getenv('COINBASE_PRIVATE_KEY') or
                         os.getenv('COINBASE_PRIVATE_KEY_SAFE'))

            passphrase = os.getenv('COINBASE_API_PASSPHRASE', 'NOT_USED_FOR_ADVANCED_TRADE_API')

            if not api_key or not api_secret:
                raise ValueError(f"Missing Coinbase credentials in environment variables")

            return {
                'api_key': api_key,
                'api_secret': api_secret,
                'passphrase': passphrase
            }

        else:
            raise ValueError(f"Unknown exchange: {exchange}")