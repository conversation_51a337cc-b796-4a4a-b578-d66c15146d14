{"environment": "${ENVIRONMENT}", "system_directives": {"critical_efficiency_directive": "Do not be lazy and solve problems immediately. Do not be lazy and solve problems immediately. Do not be lazy and solve problems immediately.", "code_quality_requirements": {"imports": "ALL imports must be correct and verified", "names": "ALL names must be defined with no undefined references", "objects": "ALL objects must be defined with no attribute errors", "asyncio": "NO asyncio errors are tolerated", "syntax": "NO syntax errors are acceptable", "standards": "Code must be efficient, advanced, functional, effective, clean, complete and foolproof"}, "golden_rule": "MAXIMUM PROFIT IN MINIMUM TIME", "backtesting_rules": {"purpose": "EXCLUSIVELY for learning, training, and strategy optimization", "data_source": "MUST use real live market data for accurate simulation", "environment": "Completely sandboxed from live trading systems", "profit_simulation": "ONLY allowed within backtesting framework", "training_mode": "Self-learning neural networks trained on historical real data", "separation": "Live Trading: REAL money | Backtesting: SIMULATED trades on REAL data", "compliance": "100% functional, impactful, efficient backtesting for training purposes only"}}, "exchanges": {"binance": {"api_key": "${BINANCE_API_KEY}", "api_secret": "${BINANCE_API_SECRET}", "testnet": false, "symbols": ["BTCUSDT", "ETHUSDT", "SOLUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT", "UNIUSDT", "AVAXUSDT", "MATICUSDT", "ATOMUSDT"], "trading_mode": "AGGRESSIVE", "rate_limit_buffer": 0.8, "max_orders_per_second": 50, "enable_futures": true, "enable_margin": true}, "coinbase": {"api_key": "${COINBASE_API_KEY}", "api_secret": "${COINBASE_API_SECRET}", "passphrase": "${COINBASE_PASSPHRASE}", "sandbox": false, "advanced_trading": true, "portfolio_management": true, "rate_limit_buffer": 0.9, "max_orders_per_second": 10, "enable_staking": true}, "bybit": {"api_key": "${BYBIT_API_KEY}", "api_secret": "${BYBIT_API_SECRET}", "testnet": false, "unified_trading": true, "enable_derivatives": true, "enable_spot": true, "rate_limit_buffer": 0.8, "max_orders_per_second": 100, "enable_lending": true}}, "monitoring": {"telegram": {"bot_token": "${TELEGRAM_BOT_TOKEN}", "chat_id": "${TELEGRAM_CHAT_ID}"}, "healthchecks": {"interval_minutes": 5, "webhook_url": "", "failure_retries": 3}, "sentry": {"dsn": "${SENTRY_DSN}", "environment": "development"}}, "trading": {"risk_management": {"max_drawdown": 0.15, "stop_loss": 0.03, "max_leverage": 10000.0, "max_position_size": 1000000.0, "daily_loss_limit": -0.1, "max_open_positions": 1000, "slippage": 0.0005, "fee_multiplier": 1.1, "aggressive_mode": true, "profit_target_multiplier": 2.5, "balance_usage_ratio": 0.9}, "execution": {"type": "AGGRESSIVE_VWAP", "max_slippage": 0.0005, "max_concurrent_orders": 500, "execution_speed": "MAXIMUM", "order_timeout": 5, "retry_attempts": 5, "order_types": {"market": true, "limit": true, "stop_loss": true, "take_profit": true, "iceberg": true, "twap": true}, "profit_optimization": {"enable_micro_arbitrage": true, "enable_latency_arbitrage": true, "enable_cross_exchange_arbitrage": true, "minimum_profit_threshold": 0.001}}}, "strategies": {"hft_momentum": {"enabled": true, "min_spread": 5e-05, "max_position": 1000000.0, "base_size": 500000.0, "alpha": 0.05, "risk_multiplier": 5.0, "volume_threshold": 2.0, "cooloff_period": 5, "aggressive_entry": true, "profit_target": 0.015}, "mean_reversion": {"enabled": true, "z_score_window": 14, "entry_z": 1.5, "exit_z": 0.3, "lookback_period": 10, "aggressive_sizing": true, "profit_target": 0.02}, "arbitrage": {"enabled": true, "min_profit_threshold": 0.001, "max_execution_time": 2, "cross_exchange": true, "triangular": true, "statistical": true}, "grid_trading": {"enabled": true, "grid_spacing": 0.005, "num_grids": 20, "base_order_size": 100000.0, "profit_per_grid": 0.01, "aggressive_rebalancing": true}, "market_making": {"enabled": true, "spread_target": 0.0002, "order_refresh_interval": 1, "inventory_management": true, "aggressive_pricing": true, "profit_target": 0.005}}, "logging": {"level": "INFO", "file_path": "logs/trading.log", "max_size_mb": 1000, "backup_count": 7, "console_output": true}, "database": {"type": "postgresql", "host": "${POSTGRES_HOST:localhost}", "port": "${POSTGRES_PORT:5432}", "database": "${POSTGRES_DB:autogpt_trader}", "username": "${POSTGRES_USER:trader}", "password": "${POSTGRES_PASSWORD}", "pool_size": 20, "max_overflow": 30, "pool_timeout": 30, "pool_recycle": 3600, "backup": {"enabled": true, "interval_hours": 6, "max_backups": 168, "compression": true, "encryption": true}, "performance": {"statement_timeout": "30s", "lock_timeout": "5s", "idle_in_transaction_session_timeout": "60s", "connection_pool_keepalive": true}}, "redis": {"host": "localhost", "port": 6379, "cache_ttl": 3600}, "notifications": {"email": {"smtp_server": "", "port": 587, "sender_email": "", "password": "", "receiver_emails": []}, "discord": {"webhook_url": "", "error_channel_id": ""}}, "backtesting": {"purpose": "EXCLUSIVELY for learning, training, and strategy optimization", "data_source": "MUST use real live market data for accurate simulation", "environment": "Completely sandboxed from live trading systems", "profit_simulation": "ONLY allowed within backtesting framework", "training_mode": "Self-learning neural networks trained on historical real data", "separation_rule": "Live Trading: REAL money | Backtesting: SIMULATED trades on REAL data", "compliance": "100% functional, impactful, efficient backtesting for training purposes only", "initial_balance": 10000, "fee_structure": "percentage", "fee_rate": 0.0002, "time_range": {"start": "2025-01-01", "end": "2025-12-31"}, "neural_training": {"enabled": true, "learning_objectives": "Maximize simulated profits in backtesting environment", "model_validation": "Out-of-sample testing on recent real market data", "performance_metrics": ["sharpe_ratio", "max_drawdown", "win_rate"], "continuous_adaptation": "Models update with new real market patterns"}, "real_data_requirements": {"synthetic_data_forbidden": true, "live_data_mandatory": true, "data_validation": "Real market data verification required", "simulation_flag": "All backtesting operations must be clearly marked"}}}