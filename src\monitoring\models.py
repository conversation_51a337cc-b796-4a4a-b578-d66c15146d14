# backend/src/monitoring/models.py
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("trading.models")

Base = declarative_base()

class Trade(Base):
    __tablename__ = 'trades'
    id = Column(Integer, primary_key=True)
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    symbol = Column(String(10), index=True)
    side = Column(String(4))
    amount = Column(Float)
    price = Column(Float)
    pnl = Column(Float)
    strategy = Column(String(50), index=True)
    is_open = Column(Boolean, default=True)
    fee = Column(Float)

class PerformanceMetric(Base):
    __tablename__ = 'metrics'
    id = Column(Integer, primary_key=True)
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    sharpe_ratio = Column(Float)
    max_drawdown = Column(Float)
    volatility = Column(Float)
    win_rate = Column(Float)
    total_pnl = Column(Float)
    daily_pnl = Column(Float)

# Initialize database with PostgreSQL (CRITICAL for persistent learning)
import os
from pathlib import Path

# PostgreSQL connection for persistent trading data
POSTGRES_URL = os.getenv('DATABASE_URL', 
    'postgresql://trader:AggressiveTrading2025!@localhost:5432/autogpt_trader'
)

try:
    # Primary: PostgreSQL for production trading system
    engine = create_engine(
        POSTGRES_URL,
        pool_size=10,
        max_overflow=20,
        pool_pre_ping=True,
        pool_recycle=3600,
        echo=False  # Set to True for debugging
    )
    
    # Test PostgreSQL connection
    with engine.connect() as conn:
        conn.execute(text("SELECT 1"))
    
    logger.info("✅ [DATABASE] PostgreSQL connected successfully for persistent learning")
    
except Exception as e:
    logger.warning(f"⚠️ [DATABASE] PostgreSQL connection failed: {e}")
    logger.info("🔄 [DATABASE] Falling back to SQLite (LIMITED LEARNING CAPABILITY)")
    
    # Fallback to SQLite only if PostgreSQL is unavailable
    BASE_DIR = Path(__file__).resolve().parent.parent.parent
    DB_PATH = os.path.join(BASE_DIR, 'data/trading_monitor.db')
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    
    engine = create_engine(
        f"sqlite:///{DB_PATH}",
        connect_args={"check_same_thread": False},
        pool_pre_ping=True
    )
Base.metadata.create_all(engine)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    """Database session generator for FastAPI dependency injection"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def initialize_sample_data():
    """Initialize with sample data for development"""
    db = SessionLocal()
    try:
        if not db.query(Trade).first():
            from random import uniform, choice
            from datetime import timedelta
            
            strategies = ['mean_reversion', 'momentum', 'breakout']
            symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
            
            for i in range(100):
                db.add(Trade(
                    timestamp=datetime.utcnow() - timedelta(minutes=i*10),
                    symbol=choice(symbols),
                    side=choice(['buy', 'sell']),
                    amount=uniform(0.1, 5.0),
                    price=uniform(100, 50000),
                    pnl=uniform(-200, 500),
                    strategy=choice(strategies),
                    is_open=False if i % 3 == 0 else True,
                    fee=uniform(0.001, 0.0025)
                ))
            
            db.add(PerformanceMetric(
                timestamp=datetime.utcnow(),
                sharpe_ratio=1.85,
                max_drawdown=0.15,
                volatility=0.28,
                win_rate=0.68,
                total_pnl=2845.50,
                daily_pnl=342.20
            ))
            db.commit()
            logger.info("Initialized database with sample data")
    except Exception as e:
        logger.error(f"Error initializing sample data: {e}")
        db.rollback()
    finally:
        db.close()

from sqlalchemy import TypeDecorator
from datetime import datetime, timezone

class UTCDateTime(TypeDecorator):
    impl = DateTime
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is not None:
            return value.astimezone(timezone.utc).replace(tzinfo=None)
        return None

    def process_result_value(self, value, dialect):
        if value is not None:
            return value.replace(tzinfo=timezone.utc)
        return None

if __name__ == "__main__":
    initialize_sample_data()