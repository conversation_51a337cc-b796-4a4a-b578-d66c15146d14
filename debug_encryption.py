#!/usr/bin/env python3
"""
Debug encryption/decryption system
"""

import os
import sys
from pathlib import Path

# Setup X drive environment
def setup_x_drive_environment():
    """Setup X drive environment and paths"""
    X_DRIVE_PROJECT = Path("X:/autogpt_trade_project/The_real_deal/autogpt-trader")
    X_DRIVE_SRC = X_DRIVE_PROJECT / "src"
    
    # Add to Python path
    x_drive_paths = [str(X_DRIVE_PROJECT), str(X_DRIVE_SRC)]
    for path in x_drive_paths:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    return X_DRIVE_PROJECT, X_DRIVE_SRC

PROJECT_ROOT, SRC_DIR = setup_x_drive_environment()

def test_encryption_system():
    """Test the encryption system"""
    print("🔍 [DEBUG] Testing encryption system...")
    
    # Test our secure credentials system
    try:
        from src.utils.secure_credentials import encrypt_value, decrypt_value
        print("✅ [IMPORT] Imported from src.utils.secure_credentials")
        
        # Test encryption/decryption cycle
        test_value = "organizations/7405b51f-cfea-4f54-a52d-02838b5cb217/apiKeys/66c4c378-f65b-4a7d-a23f-37d8936dc66e"
        print(f"🔤 [TEST] Original value: {test_value}")
        
        encrypted = encrypt_value(test_value)
        print(f"🔒 [TEST] Encrypted: {encrypted[:50]}...")
        
        decrypted = decrypt_value(encrypted)
        print(f"🔓 [TEST] Decrypted: {decrypted}")
        
        print(f"✅ [TEST] Round trip successful: {test_value == decrypted}")
        
        if test_value == decrypted:
            print("✅ [SUCCESS] Encryption system working correctly!")
            return True
        else:
            print("❌ [FAILED] Encryption system not working!")
            return False
            
    except Exception as e:
        print(f"❌ [ERROR] Encryption test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_existing_encrypted_values():
    """Test decryption of existing encrypted values in .env"""
    print("\n🔍 [DEBUG] Testing existing encrypted values...")
    
    try:
        from src.utils.secure_credentials import decrypt_value
        
        # Test Coinbase API key
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        if encrypted_api_key:
            print(f"🔒 [TEST] Encrypted API key: {encrypted_api_key[:50]}...")
            try:
                decrypted_api_key = decrypt_value(encrypted_api_key)
                print(f"🔓 [TEST] Decrypted API key: {decrypted_api_key}")
                
                if decrypted_api_key.startswith("organizations/"):
                    print("✅ [SUCCESS] API key decrypted correctly!")
                else:
                    print("❌ [FAILED] API key not in expected format!")
                    return False
            except Exception as e:
                print(f"❌ [ERROR] Failed to decrypt API key: {e}")
                return False
        else:
            print("❌ [ERROR] ENCRYPTED_COINBASE_API_KEY_NAME not found in environment")
            return False
        
        # Test Coinbase private key
        encrypted_private_key = os.getenv('ENCRYPTED_COINBASE_PRIVATE_KEY')
        if encrypted_private_key:
            print(f"🔒 [TEST] Encrypted private key: {encrypted_private_key[:50]}...")
            try:
                decrypted_private_key = decrypt_value(encrypted_private_key)
                print(f"🔓 [TEST] Decrypted private key: {decrypted_private_key[:50]}...")
                
                if decrypted_private_key.startswith("-----BEGIN"):
                    print("✅ [SUCCESS] Private key decrypted correctly!")
                else:
                    print("❌ [FAILED] Private key not in expected format!")
                    return False
            except Exception as e:
                print(f"❌ [ERROR] Failed to decrypt private key: {e}")
                return False
        else:
            print("❌ [ERROR] ENCRYPTED_COINBASE_PRIVATE_KEY not found in environment")
            return False
        
        print("✅ [SUCCESS] All existing encrypted values decrypted correctly!")
        return True
        
    except Exception as e:
        print(f"❌ [ERROR] Testing existing values failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_py_compatibility():
    """Test compatibility with main.py decryption"""
    print("\n🔍 [DEBUG] Testing main.py compatibility...")
    
    try:
        from src.utils.cryptography.secure_credentials import decrypt_value
        print("✅ [IMPORT] Imported from src.utils.cryptography.secure_credentials")
        
        # Test with existing encrypted values
        encrypted_api_key = os.getenv('ENCRYPTED_COINBASE_API_KEY_NAME')
        if encrypted_api_key:
            try:
                decrypted = decrypt_value(encrypted_api_key)
                print(f"🔓 [TEST] Main.py decrypt result: {decrypted}")
                
                if decrypted.startswith("organizations/"):
                    print("✅ [SUCCESS] Main.py compatibility working!")
                    return True
                else:
                    print("❌ [FAILED] Main.py compatibility not working!")
                    return False
            except Exception as e:
                print(f"❌ [ERROR] Main.py decrypt failed: {e}")
                return False
        else:
            print("❌ [ERROR] No encrypted value to test")
            return False
            
    except Exception as e:
        print(f"❌ [ERROR] Main.py compatibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function"""
    print("🚀 [DEBUG] AutoGPT Trader - Encryption Debug")
    print("=" * 60)
    
    # Test 1: Basic encryption system
    test1_result = test_encryption_system()
    
    # Test 2: Existing encrypted values
    test2_result = test_existing_encrypted_values()
    
    # Test 3: Main.py compatibility
    test3_result = test_main_py_compatibility()
    
    print("\n" + "=" * 60)
    print("📊 [RESULTS] Debug Results:")
    print(f"   Basic encryption: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Existing values: {'✅ PASS' if test2_result else '❌ FAIL'}")
    print(f"   Main.py compatibility: {'✅ PASS' if test3_result else '❌ FAIL'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 [SUCCESS] All encryption tests passed!")
        print("💰 [READY] Encryption system ready for main.py!")
        return True
    else:
        print("\n❌ [FAILED] Some encryption tests failed!")
        print("🔧 [ACTION] Fix encryption issues before running main.py")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ [CONCLUSION] Encryption system working perfectly!")
        else:
            print("\n❌ [CONCLUSION] Encryption system has issues!")
        
    except Exception as e:
        print(f"❌ [CRITICAL] Debug failed: {e}")
        import traceback
        traceback.print_exc()
